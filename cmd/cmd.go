package cmd

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/kernel"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/services/filewatch"
	"context"
	"github.com/spf13/cobra"
	"github.com/spf13/pflag"
	"github.com/spf13/viper"
	"log"
)

var defaultCfg = "conf.yaml"

var rootCmd = &cobra.Command{
	Use:   "app",
	Short: "app",
	Long:  "app",
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		return readConfig()
	},
	PreRunE: func(cmd *cobra.Command, args []string) error {
		if checkPreCompile(args) {
			cmd.SetContext(context.WithValue(cmd.Context(), "APP_PROJECT_PRE_COMPILE", true))
		}
		return kernel.Initialize(cmd.Context())
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		isCompile := false
		if checkPreCompile(args) {
			isCompile = true
			cmd.SetContext(context.WithValue(cmd.Context(), "APP_PROJECT_PRE_COMPILE", true))
		}
		if checkIsBuilt(args) {
			cmd.SetContext(context.WithValue(cmd.Context(), "APP_PROJECT_BUILT", true))
		}
		if err := registerJobs(); err != nil {
			return err
		}
		if !isCompile {
			//go filewatch.SyncFile()
			go filewatch.WatchFile()
		}
		return kernel.Run(cmd.Context())
	},
}

func checkPreCompile(args []string) bool {
	result := false
	if len(args) > 0 {
		for _, s := range args {
			if s == "APP_PROJECT_PRE_COMPILE" {
				result = true
				break
			}
		}
	}
	return result
}

func checkIsBuilt(args []string) bool {
	result := false
	if len(args) > 0 {
		for _, s := range args {
			if s == "APP_PROJECT_BUILT" {
				result = true
				break
			}
		}
	}
	return result
}

func readConfig() error {
	var err error
	viper.SetDefault("c", defaultCfg)
	// 设置环境变量前缀并自动加载环境变量
	viper.SetEnvPrefix("APP")
	viper.AutomaticEnv()

	if err = viper.BindPFlags(pflag.CommandLine); err != nil {
		return err
	}

	envC, configFile := viper.GetString("c"), defaultCfg
	configFile = envC

	viper.SetConfigFile(configFile)

	return viper.ReadInConfig()
}

func registerJobs() error {
	//if jobs.Jobs != nil && len(jobs.Jobs) > 0 {
	//	logger, _ := core.GetLogger()
	//	c := cron.New(
	//		cron.WithSeconds(),
	//	)
	//	for _, job := range jobs.Jobs {
	//		job.Name = strings.TrimSpace(job.Name)
	//		job.Spec = strings.TrimSpace(job.Spec)
	//		if job.Name == "" || job.Spec == "" || job.Cmd == nil {
	//			continue
	//		}
	//		if _, err := c.AddJob(job.Spec, job.Cmd); err != nil {
	//			return fmt.Errorf("job [%s] register err: %v", job.Name, err)
	//		}
	//		logger.Infof("job [%s] register succeed!", job.Name)
	//	}
	//	c.Start()
	//}
	return nil
}

//func watchFile() {
//	dst := core.Container.Conf.App.Server.Upload.Dst
//	fp, err := filepath.Abs(dst)
//	if err != nil {
//		log.Panicln(err)
//	}
//	if _, err := os.Stat(fp); err != nil {
//		if errors.Is(err, fs.ErrNotExist) {
//			_ = os.MkdirAll(dst, os.ModePerm)
//		} else {
//			log.Panicln(err)
//		}
//	}
//	watcher, err := fsnotify.NewWatcher()
//	if err != nil {
//		log.Fatal(err)
//	}
//	defer watcher.Close()
//
//	// Add a path.
//	err = watcher.Add(fp)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	for {
//		select {
//		case event, ok := <-watcher.Events:
//			if !ok {
//				return
//			}
//
//			fmt.Println("================================================================================")
//			if event.Has(fsnotify.Create) {
//				matched := regexp.MustCompile("\"(.+?)\" ← \"(.+?)\"").FindStringSubmatch(event.String())
//				if len(matched) > 0 { // 完成重命名文件
//					nFilename, oFilename := matched[1], matched[2]
//					log.Printf("mv %s to %s\n", oFilename, nFilename)
//				} else { // 创建文件，此时有两种情况，一是从监测范围外移动到监测范围内（只触发create事件，不触发write事件），另一种是直接在监测范围内创建文件，此时还未完成内容写入（完成写入后还会触发write事件，且可能会触发多次）
//					log.Printf("create %s\n", event.Name)
//				}
//			} else if event.Has(fsnotify.Rename) { // 重命名文件，由于把文件移动到监测范围之外不会有create事件，所以此处将rename事件视为删除
//				log.Printf("rm(rename) %s\n", event.Name)
//			} else if event.Has(fsnotify.Write) { // 新建文件并已完成写入
//				//log.Printf("create %s\n", event.Name)
//			} else if event.Has(fsnotify.Remove) {
//				log.Printf("rm %s\n", event.Name)
//			}
//			fmt.Print("\n\n")
//		case err, ok := <-watcher.Errors:
//			if !ok {
//				return
//			}
//			log.Println("error:", err)
//		}
//	}
//
//}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		log.Panicln(err)
	}
}
