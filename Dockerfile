FROM cr.ixdev.cn/cnix/cbdv/container:go1.23-ubuntu AS builder

WORKDIR /app

COPY . .

RUN go mod tidy \
    && CGO_ENABLED=1 go generate \
    && CGO_ENABLED=1 go build -o server main.go

WORKDIR /app/web


RUN wget https://mirrors.aliyun.com/nodejs-release/v20.19.0/node-v20.19.0-linux-x64.tar.gz \
        && tar -zxvf node-v20.19.0-linux-x64.tar.gz \
        && mv node-v20.19.0-linux-x64 /usr/local/node \
        && ln -s /usr/local/node/bin/node /usr/local/bin/node \
        && ln -s /usr/local/node/bin/npm /usr/local/bin/npm \
        && ln -s /usr/local/node/bin/npx /usr/local/bin/npx \
        && npm config set registry http://registry.npmmirror.com \
        && npm install --force

ENV NODE_ENV=production
ENV REACT_APP_API_SERVER_URL=/api/v1

RUN pwd && ls && npm run build

FROM ubuntu:20.04 AS runner

WORKDIR /app

COPY --from=builder /app/server ./
COPY --from=builder /app/conf.yaml ./
COPY --from=builder /app/.env ./
COPY --from=builder /app/runtime ./runtime
COPY --from=builder /app/web/build ./web
COPY --from=builder /app/entrypoint.sh /app/entrypoint.sh

ENV TZ="Asia/Shanghai"

RUN sed -i "s@http://.*archive.ubuntu.com@http://mirrors.huaweicloud.com@g" /etc/apt/sources.list \
    && sed -i "s@http://.*security.ubuntu.com@http://mirrors.huaweicloud.com@g" /etc/apt/sources.list \
    && apt-get update \
    && apt-get install wget build-essential openssh-client -y \
    && DEBIAN_FRONTEND=noninteractive TZ=Asia/Shanghai apt-get -y install tzdata \
    && ln -sf /usr/share/zoneinfo/Asia/ShangHai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && dpkg-reconfigure -f noninteractive tzdata \
    && apt-get install nginx -y \
    && chmod +x /app/entrypoint.sh

# 关键：设置所有需要写入的目录权限为 777
RUN chmod -R 777 /app \
    && mkdir -p /var/log/nginx && chmod -R 777 /var/log/nginx \
    && mkdir -p /var/lib/nginx && chmod -R 777 /var/lib/nginx \
    && mkdir -p /var/cache/nginx && chmod -R 777 /var/cache/nginx \
    && chmod -R 777 /run \
    && chmod -R 777 /tmp

# 复制 nginx 配置并设置权限
COPY --from=builder /app/nginx.conf /etc/nginx/nginx.conf
RUN chmod 644 /etc/nginx/nginx.conf

# 为 SSH 创建通用目录（任意用户运行时会使用）
RUN mkdir -p /.ssh && chmod 777 /.ssh
COPY --from=builder /app/deploy/ssh/id_rsa /.ssh/id_rsa
COPY --from=builder /app/deploy/ssh/id_rsa.pub /.ssh/id_rsa.pub
RUN chmod 600 /.ssh/id_rsa && chmod 644 /.ssh/id_rsa.pub

EXPOSE 9888 80

ENTRYPOINT ["/app/entrypoint.sh"]