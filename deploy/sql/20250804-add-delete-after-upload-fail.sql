-- 添加上传失败后删除选项到SFTP和DICOM配置
-- 这个迁移会更新现有的JSON配置，为SFTP和DICOM的upload配置添加delete_after_upload_fail字段

-- 注意：由于MySQL的JSON字段更新比较复杂，建议在应用层面处理默认值
-- 新的字段会在用户下次更新配置时自动添加到JSON中

-- 如果需要立即为所有现有配置添加默认值，可以使用以下SQL：
-- UPDATE sys_config 
-- SET sftp = JSON_SET(COALESCE(sftp, '{}'), '$.upload.delete_after_upload_fail', false)
-- WHERE id = 1 AND sftp IS NOT NULL;

-- UPDATE sys_config 
-- SET dicom = JSON_SET(COALESCE(dicom, '{}'), '$.upload.delete_after_upload_fail', false)
-- WHERE id = 1 AND dicom IS NOT NULL;
