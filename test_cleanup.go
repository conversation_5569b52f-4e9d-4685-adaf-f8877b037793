package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/services/scheduler"
)

// 简单的测试程序，用于验证清理服务
func main() {
	// 创建测试目录和文件
	testDir := "test_cleanup_dir"
	
	// 创建测试目录
	err := os.MkdirAll(testDir, 0755)
	if err != nil {
		log.Fatalf("Failed to create test directory: %v", err)
	}
	
	// 创建一些测试文件
	testFiles := []string{"test1.txt", "test2.txt", "subdir/test3.txt"}
	for _, file := range testFiles {
		fullPath := filepath.Join(testDir, file)
		
		// 创建子目录（如果需要）
		dir := filepath.Dir(fullPath)
		if dir != testDir {
			err := os.MkdirAll(dir, 0755)
			if err != nil {
				log.Printf("Failed to create subdirectory %s: %v", dir, err)
				continue
			}
		}
		
		// 创建文件
		f, err := os.Create(fullPath)
		if err != nil {
			log.Printf("Failed to create test file %s: %v", fullPath, err)
			continue
		}
		f.WriteString("test content")
		f.Close()
		
		fmt.Printf("Created test file: %s\n", fullPath)
	}
	
	fmt.Printf("Test setup completed. Test directory: %s\n", testDir)
	fmt.Println("You can now test the cleanup functionality.")
	fmt.Println("Remember to clean up the test directory manually after testing.")
}
