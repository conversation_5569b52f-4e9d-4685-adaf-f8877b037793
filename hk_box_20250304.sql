-- auto-generated definition
create table users
(
    id               bigint                  not null comment '用户id'
        primary key,
    username         varchar(255) default '' not null comment '账号',
    password         varchar(255) default '' not null comment '密码',
    company_id       varchar(255)            null comment '企业 id//运营平台生成，二次提交审核时携带，全局唯一',
    institution      varchar(255) default '' not null comment '机构名称',
    industry         varchar(255) default '' not null comment '所属行业',
    access_addr      varchar(255) default '' not null comment '接入地点',
    belonging_region varchar(255) default '' not null comment '所属区域',
    real_name        varchar(255) default '' not null comment '真实姓名',
    id_number        varchar(255) default '' not null comment '身份证号',
    email            varchar(255)            null comment '邮箱',
    phone            varchar(255)            null comment '手机号',
    audit_result     varchar(255) default '' not null comment '审核结果 //【待审核|审核通过|审核不通过】',
    audit_comment    varchar(255) default '' not null comment '审核备注 // 不通过的原因',
    is_admin         tinyint(1)   default 0  not null comment '是否超管',
    status           tinyint      default 0  not null comment '状态。0未知1正常-1锁定',
    created_at       datetime                not null comment '创建时间',
    updated_at       datetime                not null comment '更新时间',
    constraint idx_company_id
        unique (company_id),
    constraint idx_email
        unique (email),
    constraint idx_phone
        unique (phone),
    constraint idx_username
        unique (username)
)
    comment '用户表';

INSERT INTO users (id, username, password, company_id, institution, industry, access_addr, belonging_region, real_name, id_number, email, phone, audit_result, audit_comment, is_admin, status, created_at, updated_at) VALUES (1, 'admin', '$2a$10$PWpjJ0rPmgq/uvAJSQbbTuJMoC03Y9Cb8mjkx5WqiESSuwRa3OOWi', null, '', '', '', '', '', '', null, null, '', '', 1, 0, '2025-03-03 16:17:14', '2025-03-04 16:38:56');
