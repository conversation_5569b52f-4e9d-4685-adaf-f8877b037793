package core

import (
	"fmt"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config"
	"code.ixdev.cn/liush/id-gen/idgen"
	"github.com/elastic/go-elasticsearch/v8"
	"github.com/gin-gonic/gin"
	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const DefaultKey = "default"

type PkgS struct {
	Database    map[string]*gorm.DB
	Flake       *idgen.IdGenerator
	Redis       map[string]redis.UniversalClient
	RedisPool   map[string]*redsync.Redsync
	Logger      map[string]*zap.SugaredLogger
	Middlewares []gin.HandlerFunc
	// KafkaProducers map[string]*kafka.Producer
	// KafkaConsumers map[string]*kafka.Consumer
	Elasticsearchs map[string]*elasticsearch.TypedClient
}

type ContainerS struct {
	Pkg  *PkgS
	Conf *config.Config
}

var Container *ContainerS

func GetConf() config.Config {
	return *Container.Conf
}

func GetDatabase(v ...interface{}) (*gorm.DB, error) {
	name := DefaultKey
	if len(v) > 0 {
		_name, _ := v[0].(string)
		if len(_name) > 0 {
			name = _name
		}
	}
	database, ok := Container.Pkg.Database[name]
	if !ok {
		return nil, fmt.Errorf("database %s not found", name)
	}
	return database, nil
}

func GetLogger(v ...interface{}) (*zap.SugaredLogger, error) {
	name := DefaultKey
	if len(v) > 0 {
		_name, _ := v[0].(string)
		if len(_name) > 0 {
			name = _name
		}
	}
	logger, ok := Container.Pkg.Logger[name]
	if !ok {
		return nil, fmt.Errorf("logger %s not found", name)
	}
	return logger, nil
}

func GetRedis(v ...interface{}) (redis.UniversalClient, error) {
	name := DefaultKey
	if len(v) > 0 {
		_name, _ := v[0].(string)
		if len(_name) > 0 {
			name = _name
		}
	}
	instance, ok := Container.Pkg.Redis[name]
	if !ok {
		return nil, fmt.Errorf("redis %s not found", name)
	}
	return instance, nil
}

func GetRedisPool(v ...interface{}) (*redsync.Redsync, error) {
	name := DefaultKey
	if len(v) > 0 {
		_name, _ := v[0].(string)
		if len(_name) > 0 {
			name = _name
		}
	}
	pool, ok := Container.Pkg.RedisPool[name]
	if !ok {
		return nil, fmt.Errorf("redis %s not found", name)
	}
	return pool, nil
}

func GetFlake() *idgen.IdGenerator {
	return Container.Pkg.Flake
}

func GetNextId() int64 {
	return GetFlake().NextId()
}

// func GetKafkaProducer(v ...any) (*kafka.Producer, error) {
// 	name := DefaultKey
// 	if len(v) > 0 {
// 		_name, _ := v[0].(string)
// 		if len(_name) > 0 {
// 			name = _name
// 		}
// 	}
// 	pool, ok := Container.Pkg.KafkaProducers[name]
// 	if !ok {
// 		return nil, fmt.Errorf("kafka producer %s not found", name)
// 	}
// 	return pool, nil
// }

// func GetKafkaConsumer(v ...any) (*kafka.Consumer, error) {
// 	name := DefaultKey
// 	if len(v) > 0 {
// 		_name, _ := v[0].(string)
// 		if len(_name) > 0 {
// 			name = _name
// 		}
// 	}
// 	pool, ok := Container.Pkg.KafkaConsumers[name]
// 	if !ok {
// 		return nil, fmt.Errorf("kafka consumer %s not found", name)
// 	}
// 	return pool, nil
// }

func GetElasticsearch(v ...any) (*elasticsearch.TypedClient, error) {
	name := DefaultKey
	if len(v) > 0 {
		_name, _ := v[0].(string)
		if len(_name) > 0 {
			name = _name
		}
	}
	pool, ok := Container.Pkg.Elasticsearchs[name]
	if !ok {
		return nil, fmt.Errorf("elasticsearch %s not found", name)
	}
	return pool, nil
}
