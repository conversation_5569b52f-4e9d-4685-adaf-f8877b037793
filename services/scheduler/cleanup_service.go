package scheduler

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CleanupService struct {
	cron   *cron.Cron
	logger *zap.SugaredLogger
	db     *gorm.DB
}

// NewCleanupService 创建清理服务实例
func NewCleanupService() (*CleanupService, error) {
	logger, err := core.GetLogger()
	if err != nil {
		return nil, fmt.Errorf("failed to get logger: %v", err)
	}

	db, err := core.GetDatabase()
	if err != nil {
		return nil, fmt.Errorf("failed to get database: %v", err)
	}

	c := cron.New(cron.WithSeconds())

	service := &CleanupService{
		cron:   c,
		logger: logger,
		db:     db,
	}

	return service, nil
}

// Start 启动定时清理服务
func (s *CleanupService) Start() error {
	// 每天00:00:00执行清理任务
	_, err := s.cron.AddFunc("0 0 0 * * *", s.cleanupFailedUploadFiles)
	if err != nil {
		return fmt.Errorf("failed to add cleanup job: %v", err)
	}

	s.cron.Start()
	s.logger.Info("Cleanup service started, scheduled to run daily at 00:00:00")
	return nil
}

// Stop 停止定时清理服务
func (s *CleanupService) Stop() {
	if s.cron != nil {
		s.cron.Stop()
		s.logger.Info("Cleanup service stopped")
	}
}

// cleanupFailedUploadFiles 清理上传失败的文件
func (s *CleanupService) cleanupFailedUploadFiles() {
	s.logger.Info("Starting daily cleanup of failed upload files")

	// 获取系统配置
	var sysConfig model.SysConfig
	err := s.db.First(&sysConfig, model.SysConfigKey).Error
	if err != nil {
		s.logger.Errorw("Failed to get system config", "error", err)
		return
	}

	// 检查是否启用了上传失败后删除功能
	if sysConfig.Sftp == nil || !sysConfig.Sftp.Upload.DeleteAfterUploadFail {
		s.logger.Debug("Delete after upload fail is not enabled, skipping cleanup")
		return
	}

	// 获取配置的源目录路径
	srcDir := core.GetConf().App.Rev.Fisco.Tars.Src
	if srcDir == "" {
		s.logger.Error("Source directory not configured")
		return
	}

	// 转换为绝对路径
	absPath, err := filepath.Abs(srcDir)
	if err != nil {
		s.logger.Errorw("Failed to get absolute path", "path", srcDir, "error", err)
		return
	}

	// 检查目录是否存在
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		s.logger.Warnw("Source directory does not exist", "path", absPath)
		return
	}

	// 清空目录
	err = s.cleanDirectory(absPath)
	if err != nil {
		s.logger.Errorw("Failed to clean directory", "path", absPath, "error", err)
		return
	}

	s.logger.Infow("Successfully cleaned directory", "path", absPath, "time", time.Now().Format("2006-01-02 15:04:05"))
}

// cleanDirectory 清空指定目录中的所有文件和子目录
func (s *CleanupService) cleanDirectory(dirPath string) error {
	// 读取目录内容
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return fmt.Errorf("failed to read directory: %v", err)
	}

	var errors []error
	removedCount := 0

	// 删除目录中的所有内容
	for _, entry := range entries {
		fullPath := filepath.Join(dirPath, entry.Name())
		
		err := os.RemoveAll(fullPath)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to remove %s: %v", fullPath, err))
			s.logger.Warnw("Failed to remove file/directory", "path", fullPath, "error", err)
		} else {
			removedCount++
			s.logger.Debugw("Removed file/directory", "path", fullPath)
		}
	}

	s.logger.Infow("Directory cleanup completed", 
		"directory", dirPath, 
		"removed_count", removedCount, 
		"error_count", len(errors))

	// 如果有错误，返回第一个错误
	if len(errors) > 0 {
		return errors[0]
	}

	return nil
}

// ForceCleanup 手动触发清理（用于测试或手动清理）
func (s *CleanupService) ForceCleanup() error {
	s.logger.Info("Manual cleanup triggered")
	s.cleanupFailedUploadFiles()
	return nil
}
