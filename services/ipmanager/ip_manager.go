package ipmanager

import (
	"fmt"
	"net"
	"sync"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// IPManager IP状态管理器
type IPManager struct {
	logger *zap.SugaredLogger
	orm    *gorm.DB
	mutex  sync.RWMutex
}

// NewIPManager 创建IP管理器
func NewIPManager() (*IPManager, error) {
	logger, err := core.GetLogger()
	if err != nil {
		return nil, fmt.Errorf("get logger failed: %v", err)
	}

	orm, err := core.GetDatabase()
	if err != nil {
		return nil, fmt.Errorf("get database failed: %v", err)
	}

	return &IPManager{
		logger: logger,
		orm:    orm,
		mutex:  sync.RWMutex{},
	}, nil
}

// GetActiveSftpIP 获取当前活跃的SFTP IP配置
func (m *IPManager) GetActiveSftpIP() (*model.SftpIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Sftp == nil || len(sysConfig.Sftp.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no sftp ip configs found")
	}

	for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
		if ipConfig.Active {
			return &ipConfig, nil
		}
	}

	// 如果没有找到活跃的IP，返回第一个
	return &sysConfig.Sftp.Auth.IpConfigs[0], nil
}

// GetActiveDicomIP 获取当前活跃的DICOM IP配置
func (m *IPManager) GetActiveDicomIP() (*model.DicomIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Dicom == nil || len(sysConfig.Dicom.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no dicom ip configs found")
	}

	for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
		if ipConfig.Active {
			return &ipConfig, nil
		}
	}

	// 如果没有找到活跃的IP，返回第一个
	return &sysConfig.Dicom.Auth.IpConfigs[0], nil
}

// SwitchToNextSftpIP 切换到下一个可用的SFTP IP
func (m *IPManager) SwitchToNextSftpIP(currentHost string) (*model.SftpIpConfig, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Sftp == nil || len(sysConfig.Sftp.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no sftp ip configs found")
	}

	// 找到当前IP的索引
	currentIndex := -1
	for i, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
		if ipConfig.Host == currentHost {
			currentIndex = i
			break
		}
	}

	if currentIndex == -1 {
		return nil, fmt.Errorf("current host not found in ip configs")
	}

	// 寻找下一个可用的IP，最多重复3轮（原来1轮 + 额外2轮）
	nextIndex := (currentIndex + 1) % len(sysConfig.Sftp.Auth.IpConfigs)
	maxRounds := 3
	maxAttempts := len(sysConfig.Sftp.Auth.IpConfigs) * maxRounds
	attempts := 0

	for attempts < maxAttempts {
		nextIP := &sysConfig.Sftp.Auth.IpConfigs[nextIndex]

		// 检查IP可用性
		if m.checkSftpIPAvailability(nextIP) {
			// 更新active状态
			for i := range sysConfig.Sftp.Auth.IpConfigs {
				sysConfig.Sftp.Auth.IpConfigs[i].Active = (i == nextIndex)
			}

			// 保存到数据库
			if err := m.orm.Model(sysConfig).Select("sftp").Updates(sysConfig).Error; err != nil {
				return nil, fmt.Errorf("update sftp config failed: %v", err)
			}

			currentRound := attempts/len(sysConfig.Sftp.Auth.IpConfigs) + 1
			m.logger.Infow("switched to next sftp ip",
				"from", currentHost,
				"to", nextIP.Host,
				"round", currentRound,
				"attempts", attempts+1)
			return nextIP, nil
		}

		nextIndex = (nextIndex + 1) % len(sysConfig.Sftp.Auth.IpConfigs)
		attempts++

		// 记录轮询进度
		if attempts%len(sysConfig.Sftp.Auth.IpConfigs) == 0 {
			currentRound := attempts / len(sysConfig.Sftp.Auth.IpConfigs)
			m.logger.Warnw("completed round of ip checking",
				"round", currentRound,
				"total_rounds", maxRounds)
		}
	}

	return nil, fmt.Errorf("no available sftp ip found after %d rounds", maxRounds)
}

// SwitchToNextDicomIP 切换到下一个可用的DICOM IP
func (m *IPManager) SwitchToNextDicomIP(currentHost string) (*model.DicomIpConfig, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Dicom == nil || len(sysConfig.Dicom.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no dicom ip configs found")
	}

	// 找到当前IP的索引
	currentIndex := -1
	for i, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
		if ipConfig.ServerIp == currentHost {
			currentIndex = i
			break
		}
	}

	if currentIndex == -1 {
		return nil, fmt.Errorf("current host not found in ip configs")
	}

	// 寻找下一个可用的IP
	nextIndex := (currentIndex + 1) % len(sysConfig.Dicom.Auth.IpConfigs)
	attempts := 0
	maxAttempts := len(sysConfig.Dicom.Auth.IpConfigs)

	for attempts < maxAttempts {
		nextIP := &sysConfig.Dicom.Auth.IpConfigs[nextIndex]

		// 检查IP可用性
		if m.checkDicomIPAvailability(nextIP) {
			// 更新active状态
			for i := range sysConfig.Dicom.Auth.IpConfigs {
				sysConfig.Dicom.Auth.IpConfigs[i].Active = (i == nextIndex)
			}

			// 保存到数据库
			if err := m.orm.Model(sysConfig).Select("dicom").Updates(sysConfig).Error; err != nil {
				return nil, fmt.Errorf("update dicom config failed: %v", err)
			}

			m.logger.Infow("switched to next dicom ip", "from", currentHost, "to", nextIP.ServerIp)
			return nextIP, nil
		}

		nextIndex = (nextIndex + 1) % len(sysConfig.Dicom.Auth.IpConfigs)
		attempts++
	}

	return nil, fmt.Errorf("no available dicom ip found")
}

// checkSftpIPAvailability 检查SFTP IP可用性
func (m *IPManager) checkSftpIPAvailability(ipConfig *model.SftpIpConfig) bool {
	timeout := time.Duration(ipConfig.Timeout) * time.Millisecond
	if timeout == 0 {
		timeout = 5 * time.Second
	}

	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", ipConfig.Host, ipConfig.Port), timeout)
	if err != nil {
		m.logger.Warnw("sftp ip not available", "host", ipConfig.Host, "port", ipConfig.Port, "error", err)
		return false
	}
	defer conn.Close()

	m.logger.Debugw("sftp ip is available", "host", ipConfig.Host, "port", ipConfig.Port)
	return true
}

// checkDicomIPAvailability 检查DICOM IP可用性
func (m *IPManager) checkDicomIPAvailability(ipConfig *model.DicomIpConfig) bool {
	timeout := 5 * time.Second // DICOM默认超时时间

	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", ipConfig.ServerIp, ipConfig.ServerPort), timeout)
	if err != nil {
		m.logger.Warnw("dicom ip not available", "host", ipConfig.ServerIp, "port", ipConfig.ServerPort, "error", err)
		return false
	}
	defer conn.Close()

	m.logger.Debugw("dicom ip is available", "host", ipConfig.ServerIp, "port", ipConfig.ServerPort)
	return true
}

// EnsureActiveIPExists 确保存在活跃的IP配置
func (m *IPManager) EnsureActiveIPExists() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return fmt.Errorf("get sys config failed: %v", err)
	}

	updated := false

	// 检查SFTP配置
	if sysConfig.Sftp != nil && len(sysConfig.Sftp.Auth.IpConfigs) > 0 {
		hasActiveSftp := false
		for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
			if ipConfig.Active {
				hasActiveSftp = true
				break
			}
		}
		if !hasActiveSftp {
			sysConfig.Sftp.Auth.IpConfigs[0].Active = true
			updated = true
			m.logger.Infow("set first sftp ip as active", "host", sysConfig.Sftp.Auth.IpConfigs[0].Host)
		}
	}

	// 检查DICOM配置
	if sysConfig.Dicom != nil && len(sysConfig.Dicom.Auth.IpConfigs) > 0 {
		hasActiveDicom := false
		for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
			if ipConfig.Active {
				hasActiveDicom = true
				break
			}
		}
		if !hasActiveDicom {
			sysConfig.Dicom.Auth.IpConfigs[0].Active = true
			updated = true
			m.logger.Infow("set first dicom ip as active", "host", sysConfig.Dicom.Auth.IpConfigs[0].ServerIp)
		}
	}

	if updated {
		if err := m.orm.Model(sysConfig).Select("sftp", "dicom").Updates(sysConfig).Error; err != nil {
			return fmt.Errorf("update config failed: %v", err)
		}
	}

	return nil
}

// GetAllSftpIPs 获取所有SFTP IP配置
func (m *IPManager) GetAllSftpIPs() ([]model.SftpIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Sftp == nil {
		return []model.SftpIpConfig{}, nil
	}

	return sysConfig.Sftp.Auth.IpConfigs, nil
}

// GetAllDicomIPs 获取所有DICOM IP配置
func (m *IPManager) GetAllDicomIPs() ([]model.DicomIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Dicom == nil {
		return []model.DicomIpConfig{}, nil
	}

	return sysConfig.Dicom.Auth.IpConfigs, nil
}
