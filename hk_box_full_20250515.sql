-- auto-generated definition
create table file_send_record
(
    id                bigint                  not null comment '记录id'
        primary key,
    file_id           bigint       default 0  not null comment '文件id',
    send_filename     varchar(255) default '' not null comment '发送文件名称',
    send_filepath     varchar(255) default '' not null comment '发送文件路径',
    send_file_size    bigint       default 0  not null comment '发送文件大小',
    send_file_hash    varchar(64)  default '' not null comment '发送文件哈希',
    send_archive_data json                    not null comment '发送档案数据',
    send_at           datetime(3)             not null comment '发送时间',
    take_up_time      bigint       default 0  not null comment '发送耗时(单位：毫秒)',
    status            tinyint      default 0  not null comment '状态: 0待发送，1发送成功，2发送失败',
    status_detail     json                    not null comment '状态详情',
    created_at        datetime(3)             not null comment '创建时间',
    updated_at        datetime(3)             not null comment '更新时间',
    deleted_at        datetime(3)             null comment '删除时间'
)
    comment '发送记录表' collate = utf8mb4_general_ci;

create index idx_file_id
    on file_send_record (file_id);


-- auto-generated definition
create table file_upload
(
    id             bigint                  not null comment '主键id'
        primary key,
    filename       varchar(255) default '' not null comment '文件名称',
    local_filename varchar(255) default '' not null comment '本地文件名',
    filepath       varchar(255) default '' not null comment '文件路径',
    file_size      bigint       default 0  not null comment '文件大小',
    file_hash      varchar(64)  default '' not null comment '文件哈希',
    username       varchar(255) default '' not null comment '用户名',
    created_at     datetime                not null comment '创建时间',
    updated_at     datetime                not null comment '更新时间',
    deleted_at     datetime                null comment '删除时间'
)
    comment '文件上传表' collate = utf8mb4_general_ci;

create index idx_filename
    on file_upload (filename);

create index idx_username
    on file_upload (username);

-- auto-generated definition
create table rev_package_download_records
(
    id                                  varchar(20) collate utf8mb4_general_ci             not null comment '记录id'
        primary key,
    file_id                             varchar(255)                                       not null comment '文件id',
    package_id                          varchar(20) collate utf8mb4_general_ci  default '' not null comment '包id',
    package_filepath                    varchar(255) collate utf8mb4_general_ci default '' not null comment 'tar包路径',
    package_filename                    varchar(255) collate utf8mb4_general_ci default '' not null comment 'tar包文件名',
    zip_filepath                        varchar(255) collate utf8mb4_general_ci default '' not null comment 'zip包路径',
    zip_filename                        varchar(255) collate utf8mb4_general_ci default '' not null comment 'zip包文件名',
    child_filepath                      varchar(255) collate utf8mb4_general_ci default '' not null comment '子文件路径',
    child_filename                      varchar(255) collate utf8mb4_general_ci default '' not null comment '子文件名称',
    username                            varchar(255) collate utf8mb4_general_ci default '' not null comment '用户名',
    download_ip                         varchar(60)                             default '' not null comment '下载ip',
    download_useragent                  varchar(255)                            default '' not null comment 'useragent',
    download_result                     tinyint                                 default 0  not null comment '下载结果,1成功,-1失败',
    download_detail                     json                                               null comment '下载详情',
    download_consistency_compare_result tinyint                                 default 0  not null comment '一致性比对结果,1成功,-1失败',
    download_consistency_compare_detail json                                               null comment '一致性比对详情',
    download_at                         datetime(3)                                        not null comment '下载时间',
    created_at                          datetime(3)                                        not null comment '创建时间',
    updated_at                          datetime(3)                                        not null comment '更新时间'
)
    comment '接收文件下载记录表' collate = utf8mb4_general_ci;

create index idx_file_id
    on rev_package_download_records (file_id);

-- auto-generated definition
create table rev_packages
(
    id                varchar(20)             not null comment '包id'
        primary key,
    filepath          varchar(255) default '' not null comment '文件路径',
    filename          varchar(255) default '' not null comment '文件名',
    filesize          bigint       default 0  not null comment '文件大小',
    send_code         varchar(255) default '' not null comment '发送批次',
    sender_id         varchar(255) default '' not null comment '发送方id',
    timestamp         bigint       default 0  not null comment '时间戳',
    tar_hash_verified tinyint      default 0  not null comment 'tarHash是否被验证',
    zip_hash_verified tinyint      default 0  not null comment 'zipHash是否被验证',
    cert              json                    null comment '包信息',
    zips              json                    null comment 'zip文件列表',
    archived_at       datetime(3)             null comment '归档时间',
    created_at        datetime(3)             not null comment '创建时间',
    updated_at        datetime(3)             not null comment '更新时间',
    deleted_at        datetime(3)             null comment '删除时间',
    constraint idx_filepath_filename
        unique (filepath, filename, deleted_at)
)
    comment '接收包表' collate = utf8mb4_general_ci;

-- auto-generated definition
create table rev_senders
(
    id             varchar(20)             not null comment '主键id'
        primary key,
    sender_id      varchar(255) default '' not null comment '发送方id',
    sender_name    varchar(255) default '' not null comment '发送方名称',
    sender_name_py varchar(255) default '' not null comment '发送方名称拼音',
    created_at     datetime                not null comment '创建时间',
    updated_at     datetime                not null comment '更新时间',
    deleted_at     datetime                null comment '删除时间',
    constraint idx_sender
        unique (sender_id, deleted_at)
)
    comment '文件接收：发送方' collate = utf8mb4_general_ci;

-- auto-generated definition
create table users
(
    id               bigint                  not null comment '用户id'
        primary key,
    username         varchar(255) default '' not null comment '账号',
    password         varchar(255) default '' not null comment '密码',
    company_id       varchar(255)            null comment '企业 id//运营平台生成，二次提交审核时携带，全局唯一',
    institution      varchar(255) default '' not null comment '机构名称',
    industry         varchar(255) default '' not null comment '所属行业',
    access_addr      varchar(255) default '' not null comment '接入地点',
    belonging_region varchar(255) default '' not null comment '所属区域',
    real_name        varchar(255) default '' not null comment '真实姓名',
    id_number        varchar(255) default '' not null comment '身份证号',
    email            varchar(255)            null comment '邮箱',
    phone            varchar(255)            null comment '手机号',
    audit_result     varchar(255) default '' not null comment '审核结果 //【待审核|审核通过|审核不通过】',
    audit_comment    varchar(255) default '' not null comment '审核备注 // 不通过的原因',
    is_admin         tinyint(1)   default 0  not null comment '是否超管',
    status           tinyint      default 0  not null comment '状态。0未知1正常-1锁定',
    created_at       datetime                not null comment '创建时间',
    updated_at       datetime                not null comment '更新时间',
    constraint idx_company_id
        unique (company_id),
    constraint idx_email
        unique (email),
    constraint idx_phone
        unique (phone),
    constraint idx_username
        unique (username)
)
    comment '用户表' collate = utf8mb4_general_ci;

INSERT INTO users (id, username, password, company_id, institution, industry, access_addr, belonging_region, real_name, id_number, email, phone, audit_result, audit_comment, is_admin, status, created_at, updated_at) VALUES (1, 'admin', '$2a$10$PWpjJ0rPmgq/uvAJSQbbTuJMoC03Y9Cb8mjkx5WqiESSuwRa3OOWi', null, '', '', '', '', '', '', null, null, '', '', 1, 0, '2025-03-03 16:17:14', '2025-03-04 16:38:56');

create table sys_config
(
    id                   int                  not null comment '主键'
        primary key,
    rev_dl_wm_enabled    tinyint(1) default 0 not null comment '接收下载加水印',
    sender_up_wm_enabled tinyint(1) default 0 not null comment '发送上传加水印'
)
    comment '系统配置表';
INSERT INTO hk_box.sys_config (id, rev_dl_wm_enabled, sender_up_wm_enabled) VALUES (1, 1, 1);

alter table rev_package_download_records
    add column wm_enabled tinyint(1) default 0 not null comment '是否启用水印' after download_consistency_compare_detail,
    add column wm_data    json                 null comment '水印信息' after wm_enabled;

alter table file_send_record
    add column wm_enabled tinyint(1) default 0 not null comment '是否启用水印' after status_detail,
    add column wm_data    json                 null comment '水印信息' after wm_enabled;