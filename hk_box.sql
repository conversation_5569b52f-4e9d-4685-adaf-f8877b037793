DROP TABLE IF EXISTS `file_send_record`;
create table file_send_record
(
    id                bigint                  not null comment '记录id'
        primary key,
    file_id           bigint       default 0  not null comment '文件id',
    send_filename     varchar(255) default '' not null comment '发送文件名称',
    send_filepath     varchar(255) default '' not null comment '发送文件路径',
    send_file_size    bigint       default 0  not null comment '发送文件大小',
    send_file_hash    varchar(64)  default '' not null comment '发送文件哈希',
    send_archive_data json                    not null comment '发送档案数据',
    send_at           datetime(3)             not null comment '发送时间',
    take_up_time      bigint       default 0  not null comment '发送耗时(单位：毫秒)',
    status            tinyint      default 0  not null comment '状态: 0待发送，1发送成功，2发送失败',
    status_detail     json                    not null comment '状态详情',
    created_at        datetime(3)             not null comment '创建时间',
    updated_at        datetime(3)             not null comment '更新时间',
    deleted_at        datetime(3)             null comment '删除时间'
) comment '发送记录表' collate = utf8mb4_general_ci;

create index idx_file_id
    on file_send_record (file_id);

DROP TABLE IF EXISTS `file_upload`;
create table file_upload
(
    id             bigint                  not null comment '主键id'
        primary key,
    filename       varchar(255) default '' not null comment '文件名称',
    local_filename varchar(255) default '' not null comment '本地文件名',
    filepath       varchar(255) default '' not null comment '文件路径',
    file_size      bigint       default 0  not null comment '文件大小',
    file_hash      varchar(64)  default '' not null comment '文件哈希',
    username       varchar(255) default '' not null comment '用户名',
    created_at     datetime                not null comment '创建时间',
    updated_at     datetime                not null comment '更新时间',
    deleted_at     datetime null comment '删除时间'
) comment '文件上传表' collate = utf8mb4_general_ci;

create index idx_filename
    on file_upload (filename);

create index idx_username
    on file_upload (username);


DROP TABLE IF EXISTS `rev_packages`;
create table rev_packages
(
    id                varchar(20)             not null comment '包id'
        primary key,
    filepath          varchar(255) default '' not null comment '文件路径',
    filename          varchar(255) default '' not null comment '文件名',
    filesize          bigint       default 0  not null comment '文件大小',
    send_code         varchar(255) default '' not null comment '发送批次',
    sender_id         varchar(255) default '' not null comment '发送方id',
    timestamp         bigint       default 0  not null comment '时间戳',
    tar_hash_verified tinyint      default 0  not null comment 'tarHash是否验证成功',
    zip_hash_verified tinyint      default 0  not null comment 'zipHash是否验证成功',
    cert              json null comment '包信息',
    zips              json null comment 'zip文件列表',
    created_at        datetime(3)             not null comment '创建时间',
    updated_at        datetime(3)             not null comment '更新时间',
    deleted_at        datetime(3)             null comment '删除时间',
    constraint idx_filepath_filename
        unique (filepath, filename, deleted_at)
) comment '接收包表' collate = utf8mb4_general_ci;

