services:
  hk_box:
    container_name: hk_box
    user: "${UID}:${GID}"
    image: cr.ixdev.cn/cnix/cbdv/hk-box-be:feature-20250121-20250115-bb172f35
    ports:
      - "8892:80"
    volumes:
      - /root/docker/hk-box/be/resources:/app/resources
      - /root/docker/hk-box/be/conf.yaml:/app/conf.yaml
      - /watermark:/watermark
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      - cnix

networks:
  cnix:
    external: true