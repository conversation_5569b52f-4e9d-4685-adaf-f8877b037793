package model

type DicomIpConfig struct {
	ServerAet  string `json:"server_aet" xml:"server_aet" yaml:"server_aet"`
	ServerIp   string `json:"server_ip" xml:"server_ip" yaml:"server_ip"`
	ServerPort int16  `json:"server_port" xml:"server_port" yaml:"server_port"`
	Active     bool   `json:"active" xml:"active" yaml:"active"`
}

type SysConfigDicomAuth struct {
	ServerAet   string          `json:"server_aet" xml:"server_aet" yaml:"server_aet"`
	ServerIp    string          `json:"server_ip" xml:"server_ip" yaml:"server_ip"`
	ServerPort  uint16          `json:"server_port" xml:"server_port" yaml:"server_port"`
	DnsServer   string          `json:"dns_server" xml:"dns_server" yaml:"dns_server"`
	ResolveType string          `json:"resolve_type" xml:"resolve_type" yaml:"resolve_type"`
	IpConfigs   []DicomIpConfig `json:"ip_configs" xml:"ip_configs" yaml:"ip_configs"`
}

type SysConfigDicomUpload struct {
	AllowedFileTypes []string `json:"allowed_file_types" xml:"allowed_file_types" yaml:"allowed_file_types"`
	DstServer        string   `json:"dst_server" xml:"dst_server" yaml:"dst_server"`
}

type SysConfigDicom struct {
	Enabled bool                 `json:"enabled" xml:"enabled" yaml:"enabled"`
	Auth    SysConfigDicomAuth   `json:"auth" xml:"auth" yaml:"auth"`
	Upload  SysConfigDicomUpload `json:"upload" xml:"upload" yaml:"upload"`
}

// 用于SFTP 上传配置的IP 列表定义
type SftpIpConfig struct {
	Type                 int    `json:"type" xml:"type" yaml:"type"` // 0:password, 1:key
	Host                 string `json:"host" xml:"host" yaml:"host"`
	Port                 uint16 `json:"port" xml:"port" yaml:"port"`
	Username             string `json:"username" xml:"username" yaml:"username"`
	Timeout              uint32 `json:"timeout" xml:"timeout" yaml:"timeout"`
	Password             string `json:"password" xml:"password" yaml:"password"`
	PrivateKeyType       uint8  `json:"private_key_type" xml:"private_key_type" yaml:"private_key_type"`
	PrivateKeyContent    string `json:"private_key_content" xml:"private_key_content" yaml:"private_key_content"`
	PrivateKeyPath       string `json:"private_key_path" xml:"private_key_path" yaml:"private_key_path"`
	PrivateKeyPassphrase string `json:"private_key_passphrase" xml:"private_key_passphrase" yaml:"private_key_passphrase"`
	Active               bool   `json:"active" xml:"active" yaml:"active"`
}

type SysConfigSftpAuth struct {
	Type                 int            `json:"type" xml:"type" yaml:"type"` // 0:password, 1:key
	Host                 string         `json:"host" xml:"host" yaml:"host"`
	Port                 uint16         `json:"port" xml:"port" yaml:"port"`
	Username             string         `json:"username" xml:"username" yaml:"username"`
	Password             string         `json:"password" xml:"password" yaml:"password"`
	PrivateKeyType       int            `json:"private_key_type" xml:"private_key_type" yaml:"private_key_type"`
	PrivateKeyPath       string         `json:"private_key_path" xml:"private_key_path" yaml:"private_key_path"`
	PrivateKeyContent    string         `json:"private_key_content" xml:"private_key_content" yaml:"private_key_content"`
	PrivateKeyPassphrase string         `json:"private_key_passphrase" xml:"private_key_passphrase" yaml:"private_key_passphrase"`
	Timeout              int64          `json:"timeout" xml:"timeout" yaml:"timeout"`
	DnsServer            string         `json:"dns_server" xml:"dns_server" yaml:"dns_server"`
	ResolveType          string         `json:"resolve_type" xml:"resolve_type" yaml:"resolve_type"`
	IpConfigs            []SftpIpConfig `json:"ip_configs" xml:"ip_configs" yaml:"ip_configs"`
}

type SysConfigSftpUpload struct {
	DstDir            string   `json:"dst_dir" xml:"dst_dir" yaml:"dst_dir"`
	AllowedFileTypes  []string `json:"allowed_file_types" xml:"allowed_file_types" yaml:"allowed_file_types"`
	DeleteAfterUpload bool     `json:"delete_after_upload" xml:"delete_after_upload" yaml:"delete_after_upload"`
}

type SysConfigSftp struct {
	Enabled bool                `json:"enabled" xml:"enabled" yaml:"enabled"`
	Auth    SysConfigSftpAuth   `json:"auth" xml:"auth" yaml:"auth"`
	Upload  SysConfigSftpUpload `json:"upload" xml:"upload" yaml:"upload"`
}

type SysConfig struct {
	Id                int64           `json:"id" xml:"id" yaml:"id" gorm:"primaryKey"`
	RevDlWmEnabled    bool            `json:"rev_dl_wm_enabled" xml:"rev_dl_wm_enabled" yaml:"rev_dl_wm_enabled"`
	SenderUpWmEnabled bool            `json:"sender_up_wm_enabled" xml:"sender_up_wm_enabled" yaml:"sender_up_wm_enabled"`
	Dicom             *SysConfigDicom `json:"dicom" xml:"dicom" yaml:"dicom" gorm:"serializer:json"`
	Sftp              *SysConfigSftp  `json:"sftp" xml:"sftp" yaml:"sftp" gorm:"serializer:json"`
}

func (SysConfig) TableName() string {
	return "sys_config"
}

var (
	SysConfigKey = 1
)
